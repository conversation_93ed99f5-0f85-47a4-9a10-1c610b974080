package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.ConversionException;
import com.talkweb.ai.indexer.core.ConversionResult;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class ExcelToMarkdownConverterTest {

    private ExcelToMarkdownConverter converter;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        // Create converter with default constructor
        converter = new ExcelToMarkdownConverter();
    }

    @Test
    void testSupportsExtension() {
        assertTrue(converter.supportsExtension("xlsx"));
        assertTrue(converter.supportsExtension("xls"));
        assertTrue(converter.supportsExtension("xlsm")); // New support
        assertTrue(converter.supportsExtension("XLSX")); // Case insensitive
        assertFalse(converter.supportsExtension("csv"));
        assertFalse(converter.supportsExtension(null));
    }

    @Test
    void testConvertExcelToMarkdown() throws IOException, ConversionException {
        File excelFile = createTestExcelFile("test.xlsx");

        ConversionResult result = converter.convert(excelFile);

        assertNotNull(result);
        assertEquals(ConversionResult.Status.SUCCESS, result.getStatus());
        assertNotNull(result.getContent());

        // Test enhanced features
        String content = result.getContent();

        // Should contain document metadata
        assertTrue(content.contains("# test.xlsx"));
        assertTrue(content.contains("**文件信息:**"));
        assertTrue(content.contains("- 文件名: test.xlsx"));
        assertTrue(content.contains("- 工作表数量: 1"));

        // Should contain sheet content
        assertTrue(content.contains("## Sheet1"));
        assertTrue(content.contains("| Header 1 | Header 2 |"));
        assertTrue(content.contains("| R1C1 | R1C2 |"));
        assertTrue(content.contains("| R2C1 | R2C2 |"));

        // Should have proper table formatting
        assertTrue(content.contains("|---|"));
    }

    @Test
    void testEnhancedCacheStatistics() {
        Map<String, Integer> stats = ExcelToMarkdownConverter.getCacheStatistics();
        assertNotNull(stats);
        assertTrue(stats.containsKey("cellContentCacheSize"));
        assertTrue(stats.containsKey("cellContentCacheLimit"));
        assertEquals(1000, stats.get("cellContentCacheLimit").intValue());
    }

    @Test
    void testCacheClear() {
        ExcelToMarkdownConverter.clearCache();
        Map<String, Integer> stats = ExcelToMarkdownConverter.getCacheStatistics();
        assertEquals(0, stats.get("cellContentCacheSize").intValue());
    }

    private File createTestExcelFile(String fileName) throws IOException {
        Path filePath = tempDir.resolve(fileName);
        try (Workbook workbook = new XSSFWorkbook();
             FileOutputStream fos = new FileOutputStream(filePath.toFile())) {
            Sheet sheet = workbook.createSheet("Sheet1");

            // Header
            Row headerRow = sheet.createRow(0);
            Cell header1 = headerRow.createCell(0);
            header1.setCellValue("Header 1");
            Cell header2 = headerRow.createCell(1);
            header2.setCellValue("Header 2");

            // Data Row 1
            Row row1 = sheet.createRow(1);
            row1.createCell(0).setCellValue("R1C1");
            row1.createCell(1).setCellValue("R1C2");

            // Data Row 2
            Row row2 = sheet.createRow(2);
            row2.createCell(0).setCellValue("R2C1");
            row2.createCell(1).setCellValue("R2C2");

            workbook.write(fos);
        }
        return filePath.toFile();
    }

    @Test
    void testMergedCellHandling() throws IOException, ConversionException {
        File excelFile = createTestExcelFileWithMergedCells("merged.xlsx");

        ConversionResult result = converter.convert(excelFile);

        assertNotNull(result);
        assertEquals(ConversionResult.Status.SUCCESS, result.getStatus());

        String content = result.getContent();
        assertTrue(content.contains("Merged Header"));
        assertTrue(content.contains("## Merged Sheet"));
    }

    private File createTestExcelFileWithMergedCells(String fileName) throws IOException {
        Path filePath = tempDir.resolve(fileName);
        try (Workbook workbook = new XSSFWorkbook();
             FileOutputStream fos = new FileOutputStream(filePath.toFile())) {
            Sheet sheet = workbook.createSheet("Merged Sheet");

            // Create merged header
            Row headerRow = sheet.createRow(0);
            Cell mergedCell = headerRow.createCell(0);
            mergedCell.setCellValue("Merged Header");

            // Merge cells A1:C1
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 2));

            // Create data row
            Row dataRow = sheet.createRow(1);
            dataRow.createCell(0).setCellValue("Data1");
            dataRow.createCell(1).setCellValue("Data2");
            dataRow.createCell(2).setCellValue("Data3");

            workbook.write(fos);
        }
        return filePath.toFile();
    }
}
