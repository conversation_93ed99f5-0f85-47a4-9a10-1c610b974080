
package com.talkweb.ai.indexer.config;

import com.talkweb.ai.indexer.core.impl.*;
import com.talkweb.ai.indexer.core.Plugin;
import com.talkweb.ai.indexer.core.PluginMetadata;
import com.talkweb.ai.indexer.core.adapter.ConverterPluginAdapterFactory;
import com.talkweb.ai.indexer.util.RtfToMarkdownConverter;
import com.talkweb.ai.indexer.util.OdtToMarkdownConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ConverterPluginConfig {

    @Bean
    public Plugin htmlConverterPlugin() {
        return new HtmlToMarkdownConverter(
            PluginMetadata.builder()
                .id("html-converter")
                .name("HTML to Markdown Converter")
                .version("1.0.0")
                .description("Converts HTML/HTML files to Markdown format")
                .build()
        );
    }

    @Bean
    public Plugin pdfConverterPlugin() {
        PdfToMarkdownConverter converter = new PdfToMarkdownConverter();
        PluginMetadata metadata = PluginMetadata.builder()
                .id("pdf-converter")
                .name("PDF to Markdown Converter")
                .version("3.0.0")
                .description("Converts PDF files to Markdown format with structure preservation and page splitting")
                .className(PdfToMarkdownConverter.class.getName())
                .build();

        return ConverterPluginAdapterFactory.createAdapter(converter, metadata);
    }

    @Bean
    public Plugin excelConverterPlugin() {
        ExcelToMarkdownConverter converter = new ExcelToMarkdownConverter();
        PluginMetadata metadata = PluginMetadata.builder()
                .id("excel-converter")
                .name("Excel to Markdown Converter")
                .version("3.0.0")
                .description("Converts Excel files (.xls, .xlsx, .xlsm) to Markdown tables with structure preservation")
                .className(ExcelToMarkdownConverter.class.getName())
                .build();

        return ConverterPluginAdapterFactory.createAdapter(converter, metadata);
    }

    @Bean
    public Plugin rtfConverterPlugin() {
        return new RtfToMarkdownConverter(
            PluginMetadata.builder()
                .id("rtf-converter")
                .name("RTF to Markdown Converter")
                .version("1.0.0")
                .description("Converts RTF (Rich Text Format) files to Markdown format")
                .className("com.talkweb.ai.indexer.util.RtfToMarkdownConverter")
                .build()
        );
    }

    @Bean
    public Plugin odtConverterPlugin() {
        return new OdtToMarkdownConverter(
            PluginMetadata.builder()
                .id("odt-converter")
                .name("ODT to Markdown Converter")
                .version("1.0.0")
                .description("Converts ODT (OpenDocument Text) files to Markdown format")
                .className("com.talkweb.ai.indexer.util.OdtToMarkdownConverter")
                .build()
        );
    }
}
